<script module lang="ts">
	import { defineMeta } from '@storybook/addon-svelte-csf';
	import Button from './button.svelte';

	const { Story } = defineMeta({
		title: 'UI/Button',
		component: Button,
		tags: ['autodocs'],
		argTypes: {
			variant: {
				control: { type: 'select' },
				options: ['default', 'destructive', 'outline', 'secondary', 'ghost', 'link'],
			},
			size: {
				control: { type: 'select' },
				options: ['default', 'sm', 'lg', 'icon'],
			},
		},
	});
</script>

{#snippet button()}
	Button
{/snippet}
{#snippet deleteButton()}
	Delete
{/snippet}
{#snippet outline()}
	Outline
{/snippet}
{#snippet small()}
	Small
{/snippet}

<Story name="Default" args={{ children: button }} />
<Story name="Destructive" args={{ variant: 'destructive', children: deleteButton }} />
<Story name="Outline" args={{ variant: 'outline', children: outline }} />
<Story name="Small" args={{ size: 'sm', children: small }} />
