import { getRequestEvent } from '$app/server';
import { type Cookies } from '@sveltejs/kit';
import { redirect } from 'sveltekit-flash-message/server';

/**
 * Ensure a user is present in the current request. If not, redirect to the
 * sign in page with a `next` parameter that points back to the current path.
 */
export const requireUser = async (cookies: Cookies) => {
	const { url, locals } = getRequestEvent();
	const { user } = await locals.getSession();
	const path = url.pathname || '/';
	if (!user) {
		throw redirect(
			`/auth/signin?next=${encodeURIComponent(path)}`,
			{ type: 'error', message: 'Please sign in to view.' },
			cookies,
		);
	}
	return { user };
};
