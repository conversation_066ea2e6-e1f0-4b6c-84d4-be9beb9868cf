import { getProjectForClientWithStages } from '$lib/project_utils';
import type { ServerLoad } from '@sveltejs/kit';
import { redirect } from 'sveltekit-flash-message/server';
import { requireUser } from '$lib/server/auth';

export const load: ServerLoad = async ({ params, locals, cookies }) => {
	await requireUser(cookies);

	const { supabase } = locals;
	const { client_name, project_name, org_name } = params;

	if (!org_name) {
		return redirect('/', { type: 'error', message: 'Organization not found' }, cookies);
	}

	if (!client_name) {
		return redirect('/', { type: 'error', message: 'Client not found' }, cookies);
	}

	if (!project_name) {
		return redirect('/', { type: 'error', message: 'Project not found' }, cookies);
	}

	const { project, projectError } = await getProjectForClientWithStages(
		supabase,
		org_name,
		client_name,
		project_name,
	);

	if (projectError || !project) {
		console.error('Error fetching project:', projectError);
		return redirect(
			`/org/${org_name}/clients/${client_name}`,
			{ type: 'error', message: 'Project not found' },
			cookies,
		);
	}

	// 	// Check if user can edit the project
	const { data: canEditProject, error: canEditProjectError } = await supabase.rpc(
		'can_modify_project',
		{
			project_id_param: project.project_id,
		},
	);

	if (canEditProjectError) {
		console.error('Error checking project edit permissions:', canEditProjectError);
	}

	// Find the current active stage (first incomplete stage)
	const currentStage =
		project.project_stage.find((stage) => !stage.date_completed) || project.project_stage?.[0];

	return {
		client_name: client_name,
		project_name: project_name,
		project: project,
		projectStages: project.project_stage,
		currentStage,
		canEditProject: canEditProject || false,
	};
};
