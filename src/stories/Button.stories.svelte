<script module>
	import { But<PERSON> } from '$lib/components/ui/button';
	import { defineMeta } from '@storybook/addon-svelte-csf';
	import { fn } from '@storybook/test';

	// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
	const { Story } = defineMeta({
		title: 'Example/Button',
		component: Button,
		tags: ['autodocs'],
		argTypes: {
			variant: {
				control: { type: 'select' },
				options: ['default', 'destructive', 'outline', 'secondary', 'ghost', 'link'],
			},
			size: {
				control: { type: 'select' },
				options: ['default', 'sm', 'lg', 'icon'],
			},
			onclick: { action: 'clicked' },
		},
		args: {
			onclick: fn(),
		},
	});
</script>

<!-- More on writing stories with args: https://storybook.js.org/docs/writing-stories/args -->
<Story name="Default" args={{ variant: 'default' }}>Button</Story>

<Story name="Secondary" args={{ variant: 'secondary' }}>Secondary</Story>

<Story name="Destructive" args={{ variant: 'destructive' }}>Destructive</Story>

<Story name="Outline" args={{ variant: 'outline' }}>Outline</Story>

<Story name="Ghost" args={{ variant: 'ghost' }}>Ghost</Story>

<Story name="Link" args={{ variant: 'link' }}>Link</Story>

<Story name="Small" args={{ size: 'sm' }}>Small</Story>

<Story name="Large" args={{ size: 'lg' }}>Large</Story>

<Story name="Icon" args={{ size: 'icon', variant: 'outline' }}>
	<svg
		xmlns="http://www.w3.org/2000/svg"
		width="24"
		height="24"
		viewBox="0 0 24 24"
		fill="none"
		stroke="currentColor"
		stroke-width="2"
		stroke-linecap="round"
		stroke-linejoin="round"
	>
		<path d="M5 12l5 5l10 -10" />
	</svg>
</Story>
