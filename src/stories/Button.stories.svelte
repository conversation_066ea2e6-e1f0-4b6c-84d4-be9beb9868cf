<script module>
	import { But<PERSON> } from '$lib/components/ui/button';
	import { defineMeta } from '@storybook/addon-svelte-csf';
	import { fn } from '@storybook/test';

	// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
	const { Story } = defineMeta({
		title: 'Button',
		component: Button,
		// tags: ['autodocs'],
		argTypes: {
			backgroundColor: { control: 'color' },
			size: {
				control: { type: 'select' },
				options: ['small', 'medium', 'large'],
			},
		},
		args: {
			onClick: fn(),
		},
	});
</script>

<!-- More on writing stories with args: https://storybook.js.org/docs/writing-stories/args -->
<Story name="Default" args={{ variant: 'default', label: 'Button' }} />

<Story name="Outline" args={{ variant: 'outline', label: 'Outline' }} />

<Story name="Destructive" args={{ variant: 'destructive', label: 'Destructive' }} />

<Story name="Small" args={{ size: 'small', label: 'Button' }} />
